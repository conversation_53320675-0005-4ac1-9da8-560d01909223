<?php

namespace App\Http\Controllers\Pointage;

use App\Http\Controllers\Controller;
use App\Http\Requests\Pointage\CheckLocationRequest;
use App\Http\Resources\PointageResource;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Verification;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Carbon\Carbon;

/**
 * @group Pointage
 *
 * APIs pour la gestion du pointage des employés
 */
class PointageController extends Controller
{
    /**
     * Check Location
     *
     * Vérifie si la position de l'utilisateur est dans un rayon de 50m du site
     *
     * @authenticated
     *
     * @bodyParam site_id integer required L'ID du site à vérifier. Example: 1
     * @bodyParam latitude numeric required La latitude de l'utilisateur. Example: 33.5731
     * @bodyParam longitude numeric required La longitude de l'utilisateur. Example: -7.5898
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Position vérifiée avec succès",
     *   "message_ar": "تم التحقق من الموقع بنجاح",
     *   "data": {
     *     "is_within_range": true,
     *     "distance": 25.5,
     *     "site": {
     *       "id": 1,
     *       "name": "Chantier Centre-ville",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     }
     *   }
     * }
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Position hors du rayon autorisé",
     *   "message_ar": "الموقع خارج النطاق المسموح",
     *   "data": {
     *     "is_within_range": false,
     *     "distance": 75.2,
     *     "site": {
     *       "id": 1,
     *       "name": "Chantier Centre-ville",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     }
     *   }
     * }
     */
    public function checkLocation(CheckLocationRequest $request): JsonResponse
    {
        $user = $request->user();
        $site = Site::findOrFail($request->site_id);

        // Vérifier si l'utilisateur est assigné à ce site
        if (!$user->sites()->where('site_id', $site->id)->exists()) {
            Log::create([
                'user_id' => $user->id,
                'action' => 'check_location_unauthorized',
                'details' => "Tentative de vérification de position sur un site non assigné: {$site->name}"
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas assigné à ce site.',
                'message_ar' => 'أنت غير مخصص لهذا الموقع.'
            ], 403);
        }

        $distance = $site->distanceFrom($request->latitude, $request->longitude);
        $isWithinRange = $site->isWithinRange($request->latitude, $request->longitude);

        // Log de la vérification
        Log::create([
            'user_id' => $user->id,
            'action' => 'check_location',
            'details' => "Vérification de position - Site: {$site->name}, Distance: {$distance}m, Dans le rayon: " . ($isWithinRange ? 'Oui' : 'Non')
        ]);

        return response()->json([
            'success' => true,
            'message' => $isWithinRange ? 'Position vérifiée avec succès.' : 'Position hors du rayon autorisé.',
            'message_ar' => $isWithinRange ? 'تم التحقق من الموقع بنجاح.' : 'الموقع خارج النطاق المسموح.',
            'data' => [
                'is_within_range' => $isWithinRange,
                'distance' => round($distance, 2),
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'latitude' => $site->latitude,
                    'longitude' => $site->longitude
                ]
            ]
        ]);
    }

    /**
     * Save Pointage
     *
     * Enregistre un pointage (début ou fin)
     *
     * @authenticated
     *
     * @bodyParam site_id integer required L'ID du site. Example: 1
     * @bodyParam type string required Type de pointage: 'debut' ou 'fin'. Example: debut
     * @bodyParam latitude numeric required La latitude. Example: 33.5731
     * @bodyParam longitude numeric required La longitude. Example: -7.5898
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Pointage enregistré avec succès",
     *   "message_ar": "تم تسجيل الحضور بنجاح",
     *   "data": {
     *     "id": 1,
     *     "user": {...},
     *     "site": {...},
     *     "debut_pointage": "2025-06-05 08:00:00",
     *     "fin_pointage": null,
     *     "duree": null,
     *     "is_active": true
     *   }
     * }
     */
    public function savePointage(Request $request): JsonResponse
    {
        $request->validate([
            'site_id' => 'required|exists:sites,id',
            'type' => 'required|in:debut,fin',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $user = $request->user();
        $site = Site::findOrFail($request->site_id);

        // Vérifier si l'utilisateur est assigné à ce site
        if (!$user->sites()->where('site_id', $site->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas assigné à ce site.',
                'message_ar' => 'أنت غير مخصص لهذا الموقع.'
            ], 403);
        }

        // Vérifier la proximité
        if (!$site->isWithinRange($request->latitude, $request->longitude)) {
            Log::create([
                'user_id' => $user->id,
                'action' => 'pointage_failed_distance',
                'details' => "Tentative de pointage hors rayon - Site: {$site->name}, Distance: " . $site->distanceFrom($request->latitude, $request->longitude) . "m"
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Vous êtes trop loin du site pour pointer.',
                'message_ar' => 'أنت بعيد جداً عن الموقع للتسجيل.'
            ], 400);
        }

        if ($request->type === 'debut') {
            return $this->handleDebutPointage($user, $site, $request->latitude, $request->longitude);
        } else {
            return $this->handleFinPointage($user, $site, $request->latitude, $request->longitude);
        }
    }

    /**
     * Handle début pointage
     */
    private function handleDebutPointage($user, $site, $latitude, $longitude): JsonResponse
    {
        // Vérifier s'il y a déjà un pointage actif
        $activePointage = Pointage::where('user_id', $user->id)
            ->whereNull('fin_pointage')
            ->first();

        if ($activePointage) {
            return response()->json([
                'success' => false,
                'message' => 'Vous avez déjà un pointage actif. Terminez-le d\'abord.',
                'message_ar' => 'لديك تسجيل حضور نشط بالفعل. أنهه أولاً.'
            ], 400);
        }

        $pointage = Pointage::create([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => now(),
            'debut_latitude' => $latitude,
            'debut_longitude' => $longitude,
        ]);

        Log::create([
            'user_id' => $user->id,
            'action' => 'pointage_debut',
            'details' => "Début de pointage - Site: {$site->name}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pointage de début enregistré avec succès.',
            'message_ar' => 'تم تسجيل بداية الحضور بنجاح.',
            'data' => new PointageResource($pointage->load(['user', 'site']))
        ], 201);
    }

    /**
     * Handle fin pointage
     */
    private function handleFinPointage($user, $site, $latitude, $longitude): JsonResponse
    {
        $activePointage = Pointage::where('user_id', $user->id)
            ->where('site_id', $site->id)
            ->whereNull('fin_pointage')
            ->first();

        if (!$activePointage) {
            return response()->json([
                'success' => false,
                'message' => 'Aucun pointage actif trouvé pour ce site.',
                'message_ar' => 'لم يتم العثور على تسجيل حضور نشط لهذا الموقع.'
            ], 400);
        }

        $activePointage->update([
            'fin_pointage' => now(),
            'fin_latitude' => $latitude,
            'fin_longitude' => $longitude,
        ]);

        $activePointage->calculateDuration();
        $activePointage->save();

        Log::create([
            'user_id' => $user->id,
            'action' => 'pointage_fin',
            'details' => "Fin de pointage - Site: {$site->name}, Durée: {$activePointage->duree}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pointage de fin enregistré avec succès.',
            'message_ar' => 'تم تسجيل نهاية الحضور بنجاح.',
            'data' => new PointageResource($activePointage->load(['user', 'site']))
        ]);
    }
}
