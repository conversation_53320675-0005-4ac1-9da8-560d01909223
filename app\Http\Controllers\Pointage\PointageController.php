<?php

namespace App\Http\Controllers\Pointage;

use App\Http\Controllers\Controller;
use App\Http\Requests\Pointage\CheckLocationRequest;
use App\Http\Resources\PointageResource;
use App\Http\Resources\UserResource;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Verification;
use App\Models\User;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Carbon\Carbon;

/**
 * @group Pointage
 *
 * APIs pour la gestion du pointage des employés
 */
class PointageController extends Controller
{
    /**
     * Check Location
     *
     * Vérifie si la position de l'utilisateur est dans un rayon de 50m du site
     *
     * @authenticated
     *
     * @bodyParam site_id integer required L'ID du site à vérifier. Example: 1
     * @bodyParam latitude numeric required La latitude de l'utilisateur. Example: 33.5731
     * @bodyParam longitude numeric required La longitude de l'utilisateur. Example: -7.5898
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Position vérifiée avec succès",
     *   "message_ar": "تم التحقق من الموقع بنجاح",
     *   "data": {
     *     "is_within_range": true,
     *     "distance": 25.5,
     *     "site": {
     *       "id": 1,
     *       "name": "Chantier Centre-ville",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     }
     *   }
     * }
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Position hors du rayon autorisé",
     *   "message_ar": "الموقع خارج النطاق المسموح",
     *   "data": {
     *     "is_within_range": false,
     *     "distance": 75.2,
     *     "site": {
     *       "id": 1,
     *       "name": "Chantier Centre-ville",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     }
     *   }
     * }
     */
    public function checkLocation(CheckLocationRequest $request): JsonResponse
    {
        $user = $request->user();
        $site = Site::findOrFail($request->site_id);

        // Vérifier si l'utilisateur est assigné à ce site
        if (!$user->sites()->where('site_id', $site->id)->exists()) {
            Log::create([
                'user_id' => $user->id,
                'action' => 'check_location_unauthorized',
                'details' => "Tentative de vérification de position sur un site non assigné: {$site->name}"
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas assigné à ce site.',
                'message_ar' => 'أنت غير مخصص لهذا الموقع.'
            ], 403);
        }

        $distance = $site->distanceFrom($request->latitude, $request->longitude);
        $isWithinRange = $site->isWithinRange($request->latitude, $request->longitude);

        // Log de la vérification
        Log::create([
            'user_id' => $user->id,
            'action' => 'check_location',
            'details' => "Vérification de position - Site: {$site->name}, Distance: {$distance}m, Dans le rayon: " . ($isWithinRange ? 'Oui' : 'Non')
        ]);

        return response()->json([
            'success' => true,
            'message' => $isWithinRange ? 'Position vérifiée avec succès.' : 'Position hors du rayon autorisé.',
            'message_ar' => $isWithinRange ? 'تم التحقق من الموقع بنجاح.' : 'الموقع خارج النطاق المسموح.',
            'data' => [
                'is_within_range' => $isWithinRange,
                'distance' => round($distance, 2),
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'latitude' => $site->latitude,
                    'longitude' => $site->longitude
                ]
            ]
        ]);
    }

    /**
     * Save Pointage
     *
     * Enregistre un pointage (début ou fin)
     *
     * @authenticated
     *
     * @bodyParam site_id integer required L'ID du site. Example: 1
     * @bodyParam type string required Type de pointage: 'debut' ou 'fin'. Example: debut
     * @bodyParam latitude numeric required La latitude. Example: 33.5731
     * @bodyParam longitude numeric required La longitude. Example: -7.5898
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Pointage enregistré avec succès",
     *   "message_ar": "تم تسجيل الحضور بنجاح",
     *   "data": {
     *     "id": 1,
     *     "user": {...},
     *     "site": {...},
     *     "debut_pointage": "2025-06-05 08:00:00",
     *     "fin_pointage": null,
     *     "duree": null,
     *     "is_active": true
     *   }
     * }
     */
    public function savePointage(Request $request): JsonResponse
    {
        $request->validate([
            'site_id' => 'required|exists:sites,id',
            'type' => 'required|in:debut,fin',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $user = $request->user();
        $site = Site::findOrFail($request->site_id);

        // Vérifier si l'utilisateur est assigné à ce site
        if (!$user->sites()->where('site_id', $site->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas assigné à ce site.',
                'message_ar' => 'أنت غير مخصص لهذا الموقع.'
            ], 403);
        }

        // Vérifier la proximité
        if (!$site->isWithinRange($request->latitude, $request->longitude)) {
            Log::create([
                'user_id' => $user->id,
                'action' => 'pointage_failed_distance',
                'details' => "Tentative de pointage hors rayon - Site: {$site->name}, Distance: " . $site->distanceFrom($request->latitude, $request->longitude) . "m"
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Vous êtes trop loin du site pour pointer.',
                'message_ar' => 'أنت بعيد جداً عن الموقع للتسجيل.'
            ], 400);
        }

        if ($request->type === 'debut') {
            return $this->handleDebutPointage($user, $site, $request->latitude, $request->longitude);
        } else {
            return $this->handleFinPointage($user, $site, $request->latitude, $request->longitude);
        }
    }

    /**
     * Handle début pointage
     */
    private function handleDebutPointage($user, $site, $latitude, $longitude): JsonResponse
    {
        // Vérifier s'il y a déjà un pointage actif
        $activePointage = Pointage::where('user_id', $user->id)
            ->whereNull('fin_pointage')
            ->first();

        if ($activePointage) {
            return response()->json([
                'success' => false,
                'message' => 'Vous avez déjà un pointage actif. Terminez-le d\'abord.',
                'message_ar' => 'لديك تسجيل حضور نشط بالفعل. أنهه أولاً.'
            ], 400);
        }

        $pointage = Pointage::create([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => now(),
            'debut_latitude' => $latitude,
            'debut_longitude' => $longitude,
        ]);

        Log::create([
            'user_id' => $user->id,
            'action' => 'pointage_debut',
            'details' => "Début de pointage - Site: {$site->name}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pointage de début enregistré avec succès.',
            'message_ar' => 'تم تسجيل بداية الحضور بنجاح.',
            'data' => new PointageResource($pointage->load(['user', 'site']))
        ], 201);
    }

    /**
     * Handle fin pointage
     */
    private function handleFinPointage($user, $site, $latitude, $longitude): JsonResponse
    {
        $activePointage = Pointage::where('user_id', $user->id)
            ->where('site_id', $site->id)
            ->whereNull('fin_pointage')
            ->first();

        if (!$activePointage) {
            return response()->json([
                'success' => false,
                'message' => 'Aucun pointage actif trouvé pour ce site.',
                'message_ar' => 'لم يتم العثور على تسجيل حضور نشط لهذا الموقع.'
            ], 400);
        }

        $activePointage->update([
            'fin_pointage' => now(),
            'fin_latitude' => $latitude,
            'fin_longitude' => $longitude,
        ]);

        $activePointage->calculateDuration();
        $activePointage->save();

        Log::create([
            'user_id' => $user->id,
            'action' => 'pointage_fin',
            'details' => "Fin de pointage - Site: {$site->name}, Durée: {$activePointage->duree}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Pointage de fin enregistré avec succès.',
            'message_ar' => 'تم تسجيل نهاية الحضور بنجاح.',
            'data' => new PointageResource($activePointage->load(['user', 'site']))
        ]);
    }

    /**
     * List Pointages
     *
     * Récupère la liste des pointages avec filtres (Admin uniquement)
     *
     * @authenticated
     *
     * @queryParam page integer Numéro de page pour la pagination. Example: 1
     * @queryParam per_page integer Nombre d'éléments par page (max 50). Example: 15
     * @queryParam user_id integer Filtrer par employé. Example: 2
     * @queryParam site_id integer Filtrer par site. Example: 1
     * @queryParam date_from string Filtrer à partir de cette date (Y-m-d). Example: 2025-06-01
     * @queryParam date_to string Filtrer jusqu'à cette date (Y-m-d). Example: 2025-06-30
     * @queryParam active boolean Filtrer les pointages actifs (true/false). Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "data": [
     *       {
     *         "id": 1,
     *         "user": {...},
     *         "site": {...},
     *         "debut_pointage": "2025-06-05 08:00:00",
     *         "fin_pointage": "2025-06-05 17:00:00",
     *         "duree": "09:00:00",
     *         "is_active": false
     *       }
     *     ],
     *     "current_page": 1,
     *     "total": 10,
     *     "per_page": 15
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 50);

        $query = Pointage::with(['user', 'site'])
            ->orderBy('debut_pointage', 'desc');

        // Filtres
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('debut_pointage', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('debut_pointage', '<=', $request->date_to);
        }

        if ($request->filled('active')) {
            if ($request->boolean('active')) {
                $query->whereNull('fin_pointage');
            } else {
                $query->whereNotNull('fin_pointage');
            }
        }

        $pointages = $query->paginate($perPage);

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'pointages_list',
            'details' => 'Consultation de la liste des pointages'
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'data' => PointageResource::collection($pointages->items()),
                'current_page' => $pointages->currentPage(),
                'total' => $pointages->total(),
                'per_page' => $pointages->perPage(),
                'last_page' => $pointages->lastPage()
            ]
        ]);
    }

    /**
     * Request Verification
     *
     * Demande une vérification de localisation à un employé (Admin uniquement)
     *
     * @authenticated
     *
     * @bodyParam user_id integer required L'ID de l'employé. Example: 2
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Demande de vérification envoyée",
     *   "message_ar": "تم إرسال طلب التحقق",
     *   "data": {
     *     "user": {...},
     *     "requested_at": "2025-06-05 10:30:00"
     *   }
     * }
     */
    public function requestVerification(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ], [
            'user_id.required' => 'L\'ID de l\'employé est requis. / معرف الموظف مطلوب.',
            'user_id.exists' => 'L\'employé spécifié n\'existe pas. / الموظف المحدد غير موجود.',
        ]);

        $employee = User::findOrFail($request->user_id);

        // Vérifier que c'est bien un employé
        if ($employee->role !== 'employee') {
            return response()->json([
                'success' => false,
                'message' => 'Seuls les employés peuvent recevoir des demandes de vérification.',
                'message_ar' => 'يمكن للموظفين فقط تلقي طلبات التحقق.'
            ], 400);
        }

        Log::create([
            'user_id' => $request->user()->id,
            'action' => 'verification_requested',
            'details' => "Demande de vérification envoyée à: {$employee->name}"
        ]);

        // Dans une vraie application, ici on enverrait une notification push
        // ou un message à l'employé via Firebase, WebSocket, etc.

        return response()->json([
            'success' => true,
            'message' => 'Demande de vérification envoyée avec succès.',
            'message_ar' => 'تم إرسال طلب التحقق بنجاح.',
            'data' => [
                'user' => new UserResource($employee),
                'requested_at' => now()->format('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * Verify Location
     *
     * Enregistre la position de l'employé suite à une demande de vérification
     *
     * @authenticated
     *
     * @bodyParam latitude numeric required La latitude de l'employé. Example: 33.5731
     * @bodyParam longitude numeric required La longitude de l'employé. Example: -7.5898
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Position vérifiée et enregistrée",
     *   "message_ar": "تم التحقق من الموقع وتسجيله",
     *   "data": {
     *     "id": 1,
     *     "user": {...},
     *     "latitude": 33.5731,
     *     "longitude": -7.5898,
     *     "date_heure": "2025-06-05 10:35:00"
     *   }
     * }
     */
    public function verifyLocation(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ], [
            'latitude.required' => 'La latitude est requise. / خط العرض مطلوب.',
            'latitude.between' => 'La latitude doit être entre -90 et 90. / يجب أن يكون خط العرض بين -90 و 90.',
            'longitude.required' => 'La longitude est requise. / خط الطول مطلوب.',
            'longitude.between' => 'La longitude doit être entre -180 et 180. / يجب أن يكون خط الطول بين -180 و 180.',
        ]);

        $user = $request->user();

        $verification = Verification::create([
            'user_id' => $user->id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'date_heure' => now(),
        ]);

        Log::create([
            'user_id' => $user->id,
            'action' => 'location_verified',
            'details' => "Vérification de position - Lat: {$request->latitude}, Lng: {$request->longitude}"
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Position vérifiée et enregistrée avec succès.',
            'message_ar' => 'تم التحقق من الموقع وتسجيله بنجاح.',
            'data' => [
                'id' => $verification->id,
                'user' => new UserResource($user),
                'latitude' => $verification->latitude,
                'longitude' => $verification->longitude,
                'date_heure' => $verification->date_heure->format('Y-m-d H:i:s')
            ]
        ], 201);
    }
}
