<?php

// Test avec un utilisateur qui n'est pas un employé (admin)
$url = 'http://127.0.0.1:8000/api/verification/request-verification';
$token = '****************************************'; // Token admin

$data = [
    'user_id' => 1  // ID de l'admin
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n" .
                   "Authorization: Bearer " . $token . "\r\n",
        'method' => 'POST',
        'content' => json_encode($data),
        'ignore_errors' => true  // Pour capturer les réponses d'erreur
    ]
];

echo "Test avec un utilisateur non-employé (admin)...\n";
echo "URL: " . $url . "\n";
echo "Method: POST\n";
echo "Data: " . json_encode($data) . "\n";
echo "Token: " . $token . "\n\n";

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

echo "Headers de réponse:\n";
if (isset($http_response_header)) {
    foreach ($http_response_header as $header) {
        echo $header . "\n";
    }
}

echo "\nCorps de la réponse:\n";
if ($result !== FALSE) {
    echo $result . "\n";
} else {
    echo "Aucune réponse reçue\n";
}
