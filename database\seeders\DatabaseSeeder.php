<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Créer un utilisateur admin
        $admin = User::create([
            'name' => 'Admin ClockIn',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Créer des employés de test
        $employee1 = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);

        $employee2 = User::create([
            'name' => 'Fatima Zahra',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);

        $employee3 = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);

        // Créer des sites de test
        $site1 = Site::create([
            'name' => 'Chantier Centre-ville',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);

        $site2 = Site::create([
            'name' => 'Chantier Ain Sebaa',
            'latitude' => 33.6076,
            'longitude' => -7.5336
        ]);

        $site3 = Site::create([
            'name' => 'Chantier Sidi Bernoussi',
            'latitude' => 33.6297,
            'longitude' => -7.4675
        ]);

        // Assigner des employés aux sites
        Assignment::create(['user_id' => $employee1->id, 'site_id' => $site1->id]);
        Assignment::create(['user_id' => $employee1->id, 'site_id' => $site2->id]);
        Assignment::create(['user_id' => $employee2->id, 'site_id' => $site2->id]);
        Assignment::create(['user_id' => $employee2->id, 'site_id' => $site3->id]);
        Assignment::create(['user_id' => $employee3->id, 'site_id' => $site1->id]);
        Assignment::create(['user_id' => $employee3->id, 'site_id' => $site3->id]);

        echo "✅ Données de test créées avec succès !\n";
        echo "👤 Admin: <EMAIL> / password123\n";
        echo "👥 Employés: <EMAIL>, <EMAIL>, <EMAIL> / password123\n";
    }
}
