<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

/**
 * @group Authentication
 *
 * APIs pour l'authentification des utilisateurs
 */
class AuthController extends Controller
{
    /**
     * Login
     *
     * Authentifie un utilisateur avec email et mot de passe
     *
     * @bodyParam email string required L'adresse email de l'utilisateur. Example: <EMAIL>
     * @bodyParam password string required Le mot de passe de l'utilisateur. Example: password123
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Connexion réussie",
     *   "message_ar": "تم تسجيل الدخول بنجاح",
     *   "data": {
     *     "user": {
     *       "id": 1,
     *       "name": "Admin User",
     *       "email": "<EMAIL>",
     *       "role": "admin"
     *     },
     *     "token": "1|abc123..."
     *   }
     * }
     *
     * @response 401 {
     *   "success": false,
     *   "message": "Identifiants incorrects",
     *   "message_ar": "بيانات الاعتماد غير صحيحة"
     * }
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->only('email', 'password');

        $user = User::where('email', $credentials['email'])->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            // Log failed login attempt
            if ($user) {
                Log::create([
                    'user_id' => $user->id,
                    'action' => 'login_failed',
                    'details' => 'Tentative de connexion échouée - mot de passe incorrect'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Identifiants incorrects.',
                'message_ar' => 'بيانات الاعتماد غير صحيحة.'
            ], 401);
        }

        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;

        // Log successful login
        Log::create([
            'user_id' => $user->id,
            'action' => 'login_success',
            'details' => 'Connexion réussie'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Connexion réussie.',
            'message_ar' => 'تم تسجيل الدخول بنجاح.',
            'data' => [
                'user' => new UserResource($user),
                'token' => $token
            ]
        ]);
    }

    /**
     * Logout
     *
     * Déconnecte l'utilisateur authentifié
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Déconnexion réussie",
     *   "message_ar": "تم تسجيل الخروج بنجاح"
     * }
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();

        // Log logout
        Log::create([
            'user_id' => $user->id,
            'action' => 'logout',
            'details' => 'Déconnexion'
        ]);

        // Delete current token
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Déconnexion réussie.',
            'message_ar' => 'تم تسجيل الخروج بنجاح.'
        ]);
    }

    /**
     * Get User
     *
     * Récupère les informations de l'utilisateur authentifié
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": 1,
     *     "name": "Admin User",
     *     "email": "<EMAIL>",
     *     "role": "admin"
     *   }
     * }
     */
    public function user(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new UserResource($request->user())
        ]);
    }
}
