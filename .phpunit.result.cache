{"version": 1, "defects": {"Tests\\Feature\\LogStructureTest::test_logs_created_during_api_actions": 7, "Tests\\Feature\\AuthTest::test_logout_with_valid_token": 7, "Tests\\Feature\\SiteTest::test_create_site_with_employee_role": 7}, "times": {"Tests\\Feature\\AuthTest::test_login_with_valid_credentials": 0.174, "Tests\\Feature\\AuthTest::test_login_with_invalid_credentials": 0.015, "Tests\\Feature\\AuthTest::test_login_with_missing_email": 0.015, "Tests\\Feature\\AuthTest::test_login_with_invalid_email_format": 0.012, "Tests\\Feature\\LogStructureTest::test_logs_table_structure": 0.004, "Tests\\Feature\\LogStructureTest::test_log_creation_with_correct_structure": 0.007, "Tests\\Feature\\LogStructureTest::test_logs_created_during_api_actions": 0.016, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.008, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.123, "Tests\\Feature\\AuthTest::test_logout_with_valid_token": 0.011, "Tests\\Feature\\AuthTest::test_logout_without_token": 0.007, "Tests\\Feature\\SiteTest::test_create_site_with_valid_data": 0.103, "Tests\\Feature\\SiteTest::test_create_site_without_authentication": 0.005, "Tests\\Feature\\SiteTest::test_create_site_with_employee_role": 0.006, "Tests\\Feature\\SiteTest::test_create_site_with_invalid_data": 0.022, "Tests\\Feature\\SiteTest::test_create_site_with_duplicate_name": 0.019, "Tests\\Feature\\SiteTest::test_create_site_with_boundary_coordinates": 0.019}}