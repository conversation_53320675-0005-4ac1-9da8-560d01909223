<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClockIn API Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
        }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .get { background-color: #28a745; }
        .post { background-color: #007bff; }
        .put { background-color: #ffc107; color: #212529; }
        .delete { background-color: #dc3545; }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .auth-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .response {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 ClockIn API Documentation</h1>
        
        <p>Documentation complète de l'API ClockIn pour la gestion du pointage des employés avec géolocalisation.</p>
        
        <div class="auth-note">
            <strong>🔐 Authentification :</strong> Toutes les routes protégées nécessitent un token Bearer dans l'en-tête Authorization.
            <br>Format : <code>Authorization: Bearer {your-token}</code>
        </div>

        <h2>📋 Base URL</h2>
        <div class="code">http://127.0.0.1:8000/api</div>

        <h2>🔑 Authentification</h2>
        
        <div class="endpoint">
            <h3><span class="method post">POST</span> /auth/login</h3>
            <p>Authentifie un utilisateur et retourne un token d'accès.</p>
            
            <h4>Paramètres :</h4>
            <table>
                <tr><th>Paramètre</th><th>Type</th><th>Requis</th><th>Description</th></tr>
                <tr><td>email</td><td>string</td><td>Oui</td><td>Adresse email de l'utilisateur</td></tr>
                <tr><td>password</td><td>string</td><td>Oui</td><td>Mot de passe (min 6 caractères)</td></tr>
            </table>

            <h4>Exemple de requête :</h4>
            <div class="code">
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
            </div>

            <div class="response">
                <h4>Réponse de succès (200) :</h4>
                <div class="code">
{
  "success": true,
  "message": "Connexion réussie.",
  "message_ar": "تم تسجيل الدخول بنجاح.",
  "data": {
    "user": {
      "id": 1,
      "name": "Admin ClockIn",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "token": "****************************************"
  }
}
                </div>
            </div>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /auth/logout</h3>
            <p>Déconnecte l'utilisateur authentifié.</p>
            <div class="auth-note">🔒 Authentification requise</div>

            <h4>Headers requis :</h4>
            <div class="code">Authorization: Bearer {your-token}</div>

            <h4>Exemple de requête :</h4>
            <div class="code">
curl -X POST http://127.0.0.1:8000/api/auth/logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ****************************************"
            </div>

            <div class="response">
                <h4>Réponse de succès (200) :</h4>
                <div class="code">
{
  "success": true,
  "message": "Déconnexion réussie.",
  "message_ar": "تم تسجيل الخروج بنجاح."
}
                </div>
            </div>

            <div class="response" style="background: #f8d7da; border-color: #f5c6cb;">
                <h4>Réponse d'erreur (401) - Sans token :</h4>
                <div class="code">
{
  "success": false,
  "message": "Token d'authentification requis.",
  "message_ar": "رمز المصادقة مطلوب."
}
                </div>
            </div>
        </div>

        <h2>🏗️ Gestion des Sites (Admin)</h2>
        
        <div class="endpoint">
            <h3><span class="method get">GET</span> /sites</h3>
            <p>Récupère la liste de tous les sites avec pagination.</p>
            <div class="auth-note">🔒 Authentification requise - Admin uniquement</div>
            
            <h4>Paramètres de requête :</h4>
            <table>
                <tr><th>Paramètre</th><th>Type</th><th>Description</th></tr>
                <tr><td>page</td><td>integer</td><td>Numéro de page (défaut: 1)</td></tr>
                <tr><td>per_page</td><td>integer</td><td>Éléments par page (max: 50, défaut: 15)</td></tr>
                <tr><td>search</td><td>string</td><td>Recherche par nom de site</td></tr>
            </table>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /sites</h3>
            <p>Crée un nouveau site/chantier.</p>
            <div class="auth-note">🔒 Authentification requise - Admin uniquement</div>

            <h4>Headers requis :</h4>
            <div class="code">
Content-Type: application/json
Accept: application/json
Authorization: Bearer {your-token}
            </div>

            <h4>Paramètres :</h4>
            <table>
                <tr><th>Paramètre</th><th>Type</th><th>Requis</th><th>Description</th></tr>
                <tr><td>name</td><td>string</td><td>Oui</td><td>Nom du site (unique)</td></tr>
                <tr><td>latitude</td><td>numeric</td><td>Oui</td><td>Latitude (-90 à 90)</td></tr>
                <tr><td>longitude</td><td>numeric</td><td>Oui</td><td>Longitude (-180 à 180)</td></tr>
            </table>

            <h4>Exemple de requête :</h4>
            <div class="code">
curl -X POST http://127.0.0.1:8000/api/sites \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ****************************************" \
  -d '{
    "name": "Nouveau Chantier",
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
            </div>

            <div class="response">
                <h4>Réponse de succès (201) :</h4>
                <div class="code">
{
  "success": true,
  "message": "Site créé avec succès.",
  "message_ar": "تم إنشاء الموقع بنجاح.",
  "data": {
    "id": 6,
    "name": "Nouveau Chantier",
    "latitude": "33.57310000",
    "longitude": "-7.58980000",
    "created_at": "2025-06-05 08:25:26",
    "updated_at": "2025-06-05 08:25:26"
  }
}
                </div>
            </div>

            <div class="response" style="background: #f8d7da; border-color: #f5c6cb;">
                <h4>Réponse d'erreur (401) - Sans token :</h4>
                <div class="code">
{
  "success": false,
  "message": "Token d'authentification requis.",
  "message_ar": "رمز المصادقة مطلوب."
}
                </div>
            </div>

            <div class="response" style="background: #f8d7da; border-color: #f5c6cb;">
                <h4>Réponse d'erreur (422) - Validation :</h4>
                <div class="code">
{
  "message": "Le nom du site est requis. / اسم الموقع مطلوب. (and 2 more errors)",
  "errors": {
    "name": ["Le nom du site est requis. / اسم الموقع مطلوب."],
    "latitude": ["The latitude field must be a number."],
    "longitude": ["La longitude doit être entre -180 et 180."]
  }
}
                </div>
            </div>
        </div>

        <h2>👥 Gestion des Employés (Admin)</h2>
        
        <div class="endpoint">
            <h3><span class="method get">GET</span> /employees</h3>
            <p>Récupère la liste de tous les employés.</p>
            <div class="auth-note">🔒 Authentification requise - Admin uniquement</div>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /employees</h3>
            <p>Crée un nouvel employé.</p>
            <div class="auth-note">🔒 Authentification requise - Admin uniquement</div>
            
            <h4>Paramètres :</h4>
            <table>
                <tr><th>Paramètre</th><th>Type</th><th>Requis</th><th>Description</th></tr>
                <tr><td>name</td><td>string</td><td>Oui</td><td>Nom de l'employé</td></tr>
                <tr><td>email</td><td>string</td><td>Oui</td><td>Email (unique)</td></tr>
                <tr><td>password</td><td>string</td><td>Oui</td><td>Mot de passe (min 6 caractères)</td></tr>
                <tr><td>role</td><td>string</td><td>Oui</td><td>Rôle : "admin" ou "employee"</td></tr>
            </table>
        </div>

        <h2>⏰ Pointage</h2>
        
        <div class="endpoint">
            <h3><span class="method post">POST</span> /pointage/check-location</h3>
            <p>Vérifie si la position de l'utilisateur est dans un rayon de 50m du site.</p>
            <div class="auth-note">🔒 Authentification requise</div>
            
            <h4>Paramètres :</h4>
            <table>
                <tr><th>Paramètre</th><th>Type</th><th>Requis</th><th>Description</th></tr>
                <tr><td>site_id</td><td>integer</td><td>Oui</td><td>ID du site à vérifier</td></tr>
                <tr><td>latitude</td><td>numeric</td><td>Oui</td><td>Latitude de l'utilisateur</td></tr>
                <tr><td>longitude</td><td>numeric</td><td>Oui</td><td>Longitude de l'utilisateur</td></tr>
            </table>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /pointage/save-pointage</h3>
            <p>Enregistre un pointage (début ou fin).</p>
            <div class="auth-note">🔒 Authentification requise</div>
            
            <h4>Paramètres :</h4>
            <table>
                <tr><th>Paramètre</th><th>Type</th><th>Requis</th><th>Description</th></tr>
                <tr><td>site_id</td><td>integer</td><td>Oui</td><td>ID du site</td></tr>
                <tr><td>type</td><td>string</td><td>Oui</td><td>"debut" ou "fin"</td></tr>
                <tr><td>latitude</td><td>numeric</td><td>Oui</td><td>Latitude</td></tr>
                <tr><td>longitude</td><td>numeric</td><td>Oui</td><td>Longitude</td></tr>
            </table>
        </div>

        <h2>📊 Codes de Réponse</h2>
        <table>
            <tr><th>Code</th><th>Description</th></tr>
            <tr><td>200</td><td>Succès</td></tr>
            <tr><td>201</td><td>Créé avec succès</td></tr>
            <tr><td>400</td><td>Erreur de validation</td></tr>
            <tr><td>401</td><td>Non authentifié</td></tr>
            <tr><td>403</td><td>Accès refusé</td></tr>
            <tr><td>404</td><td>Ressource non trouvée</td></tr>
            <tr><td>500</td><td>Erreur serveur</td></tr>
        </table>

        <h2>🌍 Support Multilingue</h2>
        <p>Tous les messages d'erreur et de succès sont disponibles en français et en arabe dans les champs <code>message</code> et <code>message_ar</code>.</p>

        <h2>📱 Intégration Flutter</h2>
        <p>Cette API est conçue pour fonctionner avec une application Flutter. Utilisez les en-têtes suivants :</p>
        <div class="code">
Content-Type: application/json
Accept: application/json
Authorization: Bearer {your-token}
        </div>

        <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
            <p>ClockIn API v1.0 - Documentation générée automatiquement</p>
        </footer>
    </div>
</body>
</html>
