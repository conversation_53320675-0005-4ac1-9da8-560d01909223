{"name": "spatie/data-transfer-object", "description": "Data transfer objects with batteries included", "keywords": ["spatie", "data-transfer-object"], "homepage": "https://github.com/spatie/data-transfer-object", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^8.0"}, "require-dev": {"illuminate/collections": "^8.36", "larapack/dd": "^1.1", "phpunit/phpunit": "^9.5.5", "jetbrains/phpstorm-attributes": "^1.0"}, "autoload": {"psr-4": {"Spatie\\DataTransferObject\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\DataTransferObject\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "config": {"sort-packages": true}}