<?php

// Test de connexion pour obtenir un token
$url = 'http://127.0.0.1:8000/api/auth/login';
$data = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Erreur lors de la connexion\n";
} else {
    echo "Réponse de connexion:\n";
    echo $result . "\n";
    
    $response = json_decode($result, true);
    if (isset($response['data']['token'])) {
        $token = $response['data']['token'];
        echo "\nToken obtenu: " . $token . "\n";
        
        // Test de l'API sites avec le token
        echo "\nTest de l'API sites avec authentification...\n";
        
        $sites_url = 'http://127.0.0.1:8000/api/sites';
        $sites_options = [
            'http' => [
                'header' => "Content-Type: application/json\r\n" .
                           "Authorization: Bearer " . $token . "\r\n",
                'method' => 'GET'
            ]
        ];
        
        $sites_context = stream_context_create($sites_options);
        $sites_result = file_get_contents($sites_url, false, $sites_context);
        
        if ($sites_result === FALSE) {
            echo "Erreur lors de l'accès à l'API sites\n";
        } else {
            echo "Réponse de l'API sites:\n";
            echo $sites_result . "\n";
        }
    }
}
