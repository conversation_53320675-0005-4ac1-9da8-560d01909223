<?php

// Test simple pour vérifier que le serveur fonctionne
$url = 'http://127.0.0.1:8001/';

$options = [
    'http' => [
        'method' => 'GET'
    ]
];

echo "Test de santé du serveur...\n";
echo "URL: " . $url . "\n";
echo "Method: GET\n\n";

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Erreur lors de la requête\n";
    
    // Afficher les headers de réponse
    if (isset($http_response_header)) {
        echo "Headers de réponse:\n";
        foreach ($http_response_header as $header) {
            echo $header . "\n";
        }
    }
} else {
    echo "Réponse reçue (premiers 200 caractères):\n";
    echo substr($result, 0, 200) . "...\n";
}
