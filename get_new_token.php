<?php

// Script pour obtenir un nouveau token d'authentification
$url = 'http://127.0.0.1:8000/api/auth/login';

$data = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

echo "Tentative de connexion pour obtenir un nouveau token...\n";
echo "URL: " . $url . "\n";
echo "Email: " . $data['email'] . "\n\n";

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Erreur lors de la connexion\n";
    
    // Afficher les headers de réponse
    if (isset($http_response_header)) {
        echo "Headers de réponse:\n";
        foreach ($http_response_header as $header) {
            echo $header . "\n";
        }
    }
} else {
    echo "Connexion réussie !\n";
    echo "Réponse:\n";
    $response = json_decode($result, true);
    
    if (isset($response['data']['token'])) {
        echo "Nouveau token: " . $response['data']['token'] . "\n";
    } else {
        echo $result . "\n";
    }
}
