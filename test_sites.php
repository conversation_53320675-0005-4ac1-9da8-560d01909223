<?php

// Test de l'endpoint sites pour vérifier que l'authentification fonctionne
$url = 'http://127.0.0.1:8001/api/sites';
$token = '****************************************'; // Token admin

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n" .
                   "Authorization: Bearer " . $token . "\r\n",
        'method' => 'GET'
    ]
];

echo "Test de l'endpoint sites...\n";
echo "URL: " . $url . "\n";
echo "Method: GET\n";
echo "Token: " . $token . "\n\n";

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Erreur lors de la requête\n";
    
    // Afficher les headers de réponse
    if (isset($http_response_header)) {
        echo "Headers de réponse:\n";
        foreach ($http_response_header as $header) {
            echo $header . "\n";
        }
    }
} else {
    echo "Réponse reçue:\n";
    echo $result . "\n";
}
