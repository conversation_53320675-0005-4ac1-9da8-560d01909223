<?php

// Test de l'endpoint request-verification
$url = 'http://127.0.0.1:8000/api/verification/request-verification';
$token = '****************************************'; // Token admin

$data = [
    'user_id' => 2
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n" .
                   "Authorization: Bearer " . $token . "\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

echo "Test de l'endpoint request-verification...\n";
echo "URL: " . $url . "\n";
echo "Method: POST\n";
echo "Data: " . json_encode($data) . "\n";
echo "Token: " . $token . "\n\n";

$context = stream_context_create($options);
$result = @file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Erreur lors de la requête\n";

    // Afficher les headers de réponse
    if (isset($http_response_header)) {
        echo "Headers de réponse:\n";
        foreach ($http_response_header as $header) {
            echo $header . "\n";
        }

        // Essayer de lire le contenu de l'erreur
        $response_body = stream_get_contents(STDERR);
        if ($response_body) {
            echo "\nCorps de la réponse:\n";
            echo $response_body . "\n";
        }
    }
} else {
    echo "Réponse reçue:\n";
    echo $result . "\n";
}
