<?php

namespace Tests\Feature;

use App\Models\Site;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class SiteTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur admin pour les tests
        $this->admin = User::create([
            'name' => 'Admin Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Créer un utilisateur employé pour les tests
        $this->employee = User::create([
            'name' => 'Employee Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);
    }

    /**
     * Test successful site creation with valid data.
     */
    public function test_create_site_with_valid_data(): void
    {
        // Créer un token pour l'admin
        $token = base64_encode($this->admin->id . '|' . time() . '|' . $this->admin->email);

        $siteData = [
            'name' => 'Nouveau Chantier Test',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ];

        $response = $this->postJson('/api/sites', $siteData, [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Site créé avec succès.'
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'message_ar',
                    'data' => [
                        'id',
                        'name',
                        'latitude',
                        'longitude',
                        'created_at'
                    ]
                ]);

        // Vérifier que le site a été créé en base
        $this->assertDatabaseHas('sites', [
            'name' => 'Nouveau Chantier Test',
            'latitude' => '33.57310000',
            'longitude' => '-7.58980000'
        ]);
    }

    /**
     * Test site creation without authentication.
     */
    public function test_create_site_without_authentication(): void
    {
        $siteData = [
            'name' => 'Site Sans Auth',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ];

        $response = $this->postJson('/api/sites', $siteData);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Token d\'authentification requis.'
                ]);
    }

    /**
     * Test site creation with employee role (should fail).
     */
    public function test_create_site_with_employee_role(): void
    {
        // Créer un token pour l'employé
        $token = base64_encode($this->employee->id . '|' . time() . '|' . $this->employee->email);

        $siteData = [
            'name' => 'Site Employé',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ];

        $response = $this->postJson('/api/sites', $siteData, [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Accès refusé. Droits administrateur requis.'
                ]);
    }

    /**
     * Test site creation with invalid data.
     */
    public function test_create_site_with_invalid_data(): void
    {
        // Créer un token pour l'admin
        $token = base64_encode($this->admin->id . '|' . time() . '|' . $this->admin->email);

        $siteData = [
            'name' => '', // Nom vide
            'latitude' => 'invalid', // Latitude invalide
            'longitude' => 200 // Longitude hors limites
        ];

        $response = $this->postJson('/api/sites', $siteData, [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'latitude', 'longitude']);
    }

    /**
     * Test site creation with duplicate name.
     */
    public function test_create_site_with_duplicate_name(): void
    {
        // Créer un site existant
        Site::create([
            'name' => 'Site Existant',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);

        // Créer un token pour l'admin
        $token = base64_encode($this->admin->id . '|' . time() . '|' . $this->admin->email);

        $siteData = [
            'name' => 'Site Existant', // Nom déjà utilisé
            'latitude' => 34.0000,
            'longitude' => -6.0000
        ];

        $response = $this->postJson('/api/sites', $siteData, [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name']);
    }

    /**
     * Test site creation with boundary coordinates.
     */
    public function test_create_site_with_boundary_coordinates(): void
    {
        // Créer un token pour l'admin
        $token = base64_encode($this->admin->id . '|' . time() . '|' . $this->admin->email);

        $siteData = [
            'name' => 'Site Limites',
            'latitude' => 90, // Limite max
            'longitude' => -180 // Limite min
        ];

        $response = $this->postJson('/api/sites', $siteData, [
            'Authorization' => 'Bearer ' . $token
        ]);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Site créé avec succès.'
                ]);
    }
}
