<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Pointage\PointageController;
use App\Http\Controllers\Site\SiteController;
use App\Http\Controllers\Verification\VerificationController;
use App\Http\Controllers\Employee\EmployeeController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques (sans authentification)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// Routes protégées par authentification
Route::middleware('simple.auth')->group(function () {
    
    // Authentification
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);
    });

    // Pointage
    Route::prefix('pointage')->group(function () {
        Route::post('/check-location', [PointageController::class, 'checkLocation']);
        Route::post('/save-pointage', [PointageController::class, 'savePointage']);
        Route::get('/pointages', [PointageController::class, 'index'])->middleware('admin');
    });

    // Vérification de localisation
    Route::prefix('verification')->group(function () {
        Route::post('/request-verification', [VerificationController::class, 'requestVerification'])->middleware(['simple.auth', 'admin']);
        Route::post('/verify-location', [PointageController::class, 'verifyLocation']);
    });

    // Gestion des chantiers (Admin seulement)
    Route::middleware(['simple.auth', 'admin'])->prefix('sites')->group(function () {
        Route::get('/', [SiteController::class, 'index']);
        Route::post('/', [SiteController::class, 'store']);
        Route::get('/{site}', [SiteController::class, 'show']);
        Route::put('/{site}', [SiteController::class, 'update']);
        Route::delete('/{site}', [SiteController::class, 'destroy']);
        Route::post('/assign-site', [SiteController::class, 'assignSite']);
    });

    // Gestion des employés (Admin seulement)
    Route::middleware(['simple.auth', 'admin'])->prefix('employees')->group(function () {
        Route::get('/', [EmployeeController::class, 'index']);
        Route::post('/', [EmployeeController::class, 'store']);
        Route::get('/{user}', [EmployeeController::class, 'show']);
        Route::put('/{user}', [EmployeeController::class, 'update']);
        Route::delete('/{user}', [EmployeeController::class, 'destroy']);
    });
});
