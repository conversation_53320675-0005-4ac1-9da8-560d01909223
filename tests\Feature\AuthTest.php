<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test successful login with valid credentials.
     */
    public function test_login_with_valid_credentials(): void
    {
        // Créer un utilisateur de test
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Tenter de se connecter
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        // Vérifier la réponse
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'message_ar',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ],
                        'token'
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);
    }

    /**
     * Test login with invalid credentials.
     */
    public function test_login_with_invalid_credentials(): void
    {
        // Créer un utilisateur de test
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        // Tenter de se connecter avec un mauvais mot de passe
        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        // Vérifier la réponse d'erreur
        $response->assertStatus(401)
                ->assertJson([
                    'success' => false
                ]);
    }

    /**
     * Test login with missing email.
     */
    public function test_login_with_missing_email(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'password' => 'password123'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    /**
     * Test login with invalid email format.
     */
    public function test_login_with_invalid_email_format(): void
    {
        $response = $this->postJson('/api/auth/login', [
            'email' => 'invalid-email',
            'password' => 'password123'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }
}
