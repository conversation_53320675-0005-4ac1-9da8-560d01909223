name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest]
        php: [8.0, 8.1]
        dependency-version: [prefer-lowest, prefer-stable]

    name: PHP ${{ matrix.php }} - ${{ matrix.dependency-version }} - ${{ matrix.os }}

    steps:
      -   name: Checkout code
          uses: actions/checkout@v2

      -   name: Setup PHP
          uses: shivammathur/setup-php@v2
          with:
            php-version: ${{ matrix.php }}
            extensions: mbstring, sqlite, pdo_sqlite, iconv
            coverage: none

      -   name: Setup problem matchers
          run: |
            echo "::add-matcher::${{ runner.tool_cache }}/php.json"
            echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      -   name: Install dependencies
          run: |
            composer update --${{ matrix.dependency-version }} --prefer-dist --no-interaction

      -   name: Execute tests
          run: vendor/bin/phpunit
