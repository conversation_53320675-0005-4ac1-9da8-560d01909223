# 🏗️ Guide Complet - Endpoint "Créer un Site"

## 🎯 Endpoint Fonctionnel

**✅ L'endpoint `POST /api/sites` fonctionne parfaitement !**

Le problème que vous rencontriez était simplement l'absence du token d'authentification.

## 🔐 Prérequis

### 1. **Authentification Obligatoire**
- ✅ Token Bearer requis
- ✅ Rôle Admin uniquement
- ✅ Token valide et non expiré

### 2. **Obtenir un Token**
```bash
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

**Réponse :**
```json
{
  "success": true,
  "data": {
    "token": "****************************************"
  }
}
```

## 🛠️ Utilisation de l'Endpoint

### **URL :** `POST http://127.0.0.1:8000/api/sites`

### **Headers Requis :**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {votre-token}
```

### **Body (JSON) :**
```json
{
  "name": "Nouveau Chantier",
  "latitude": 33.5731,
  "longitude": -7.5898
}
```

## 📱 Configuration dans Postman/Insomnia

### **Méthode 1 : Onglet Authorization**
1. **Method :** POST
2. **URL :** `http://127.0.0.1:8000/api/sites`
3. **Authorization :**
   - Type : `Bearer Token`
   - Token : `****************************************`
4. **Body :** Raw JSON

### **Méthode 2 : Onglet Headers**
1. **Method :** POST
2. **URL :** `http://127.0.0.1:8000/api/sites`
3. **Headers :**
   - `Content-Type: application/json`
   - `Accept: application/json`
   - `Authorization: Bearer ****************************************`
4. **Body :** Raw JSON

## ✅ Exemple Complet avec cURL

```bash
curl -X POST http://127.0.0.1:8000/api/sites \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ****************************************" \
  -d '{
    "name": "Chantier Exemple",
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
```

## 📊 Réponses de l'API

### **✅ Succès (201 Created)**
```json
{
  "success": true,
  "message": "Site créé avec succès.",
  "message_ar": "تم إنشاء الموقع بنجاح.",
  "data": {
    "id": 6,
    "name": "Chantier Exemple",
    "latitude": "33.57310000",
    "longitude": "-7.58980000",
    "created_at": "2025-06-05 08:25:26",
    "updated_at": "2025-06-05 08:25:26"
  }
}
```

### **❌ Erreur 401 - Sans Token**
```json
{
  "success": false,
  "message": "Token d'authentification requis.",
  "message_ar": "رمز المصادقة مطلوب."
}
```

### **❌ Erreur 403 - Rôle Insuffisant**
```json
{
  "success": false,
  "message": "Accès refusé. Droits administrateur requis.",
  "message_ar": "تم رفض الوصول. مطلوب صلاحيات المدير."
}
```

### **❌ Erreur 422 - Validation**
```json
{
  "message": "Le nom du site est requis. / اسم الموقع مطلوب. (and 2 more errors)",
  "errors": {
    "name": ["Le nom du site est requis. / اسم الموقع مطلوب."],
    "latitude": ["The latitude field must be a number."],
    "longitude": ["La longitude doit être entre -180 et 180. / يجب أن يكون خط الطول بين -180 و 180."]
  }
}
```

## 🔍 Règles de Validation

### **Champ `name` :**
- ✅ **Requis**
- ✅ **Type :** String
- ✅ **Max :** 255 caractères
- ✅ **Unique :** Pas de doublons

### **Champ `latitude` :**
- ✅ **Requis**
- ✅ **Type :** Numérique
- ✅ **Limites :** Entre -90 et 90

### **Champ `longitude` :**
- ✅ **Requis**
- ✅ **Type :** Numérique
- ✅ **Limites :** Entre -180 et 180

## 🧪 Tests Validés

**✅ 6 tests passent (26 assertions) :**

1. ✅ **Création avec données valides** → Status 201
2. ✅ **Sans authentification** → Status 401
3. ✅ **Avec rôle employé** → Status 403
4. ✅ **Données invalides** → Status 422
5. ✅ **Nom dupliqué** → Status 422
6. ✅ **Coordonnées limites** → Status 201

## 📋 Exemples de Données

### **Sites Valides :**
```json
{
  "name": "Chantier Centre-ville",
  "latitude": 33.5731,
  "longitude": -7.5898
}
```

```json
{
  "name": "Site Industriel Nord",
  "latitude": 34.0209,
  "longitude": -6.8416
}
```

### **Coordonnées Limites :**
```json
{
  "name": "Site Pôle Nord",
  "latitude": 90,
  "longitude": -180
}
```

```json
{
  "name": "Site Pôle Sud",
  "latitude": -90,
  "longitude": 180
}
```

## 🔄 Workflow Complet

1. **🔐 Se connecter** → Obtenir le token
2. **🏗️ Créer le site** → Avec token Bearer
3. **📝 Vérifier** → Site créé en base
4. **📊 Logger** → Action enregistrée

## 🎯 Token Actuel pour Tests

**Token valide :**
```
****************************************
```

**Utilisateur :** <EMAIL>  
**Rôle :** admin  
**Valide jusqu'à :** Session active

---

## 🎉 Résultat Final

**✅ L'endpoint `POST /api/sites` fonctionne parfaitement !**

- ✅ Authentification validée
- ✅ Autorisation admin vérifiée
- ✅ Validation des données complète
- ✅ Réponses JSON structurées
- ✅ Logging automatique
- ✅ Tests complets validés

**Il suffit d'ajouter le token d'authentification dans vos requêtes !** 🚀
