<?php

namespace App\Http\Requests\Pointage;

use Illuminate\Foundation\Http\FormRequest;

class CheckLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'site_id' => 'required|exists:sites,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'site_id.required' => 'L\'ID du site est requis. / معرف الموقع مطلوب.',
            'site_id.exists' => 'Le site spécifié n\'existe pas. / الموقع المحدد غير موجود.',
            'latitude.required' => 'La latitude est requise. / خط العرض مطلوب.',
            'latitude.numeric' => 'La latitude doit être un nombre. / يجب أن يكون خط العرض رقمًا.',
            'latitude.between' => 'La latitude doit être entre -90 et 90. / يجب أن يكون خط العرض بين -90 و 90.',
            'longitude.required' => 'La longitude est requise. / خط الطول مطلوب.',
            'longitude.numeric' => 'La longitude doit être un nombre. / يجب أن يكون خط الطول رقمًا.',
            'longitude.between' => 'La longitude doit être entre -180 et 180. / يجب أن يكون خط الطول بين -180 و 180.',
        ];
    }
}
