<?php

require_once 'vendor/autoload.php';

// Charger la configuration Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "=== Vérification des utilisateurs ===\n\n";

try {
    // Lister tous les utilisateurs
    $users = User::all();
    
    echo "Nombre total d'utilisateurs: " . $users->count() . "\n\n";
    
    foreach ($users as $user) {
        echo "ID: {$user->id}\n";
        echo "Nom: {$user->name}\n";
        echo "Email: {$user->email}\n";
        echo "Rôle: {$user->role}\n";
        echo "---\n";
    }
    
    // Vérifier spécifiquement l'utilisateur ID 2
    echo "\n=== Vérification utilisateur ID 2 ===\n";
    $user2 = User::find(2);
    
    if ($user2) {
        echo "✓ Utilisateur ID 2 trouvé:\n";
        echo "  Nom: {$user2->name}\n";
        echo "  Email: {$user2->email}\n";
        echo "  Rôle: {$user2->role}\n";
        echo "  Est employé: " . ($user2->isEmployee() ? 'Oui' : 'Non') . "\n";
    } else {
        echo "✗ Utilisateur ID 2 non trouvé\n";
    }
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
