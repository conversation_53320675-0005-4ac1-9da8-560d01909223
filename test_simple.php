<?php

// Test simple pour vérifier si le serveur Laravel fonctionne
$url = 'http://127.0.0.1:8001/api/auth/login';

$data = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

echo "Test de l'endpoint login...\n";
echo "URL: " . $url . "\n";
echo "Method: POST\n";
echo "Data: " . json_encode($data) . "\n\n";

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Erreur lors de la requête\n";
    
    // Afficher les headers de réponse
    if (isset($http_response_header)) {
        echo "Headers de réponse:\n";
        foreach ($http_response_header as $header) {
            echo $header . "\n";
        }
    }
} else {
    echo "Réponse reçue:\n";
    echo $result . "\n";
}
